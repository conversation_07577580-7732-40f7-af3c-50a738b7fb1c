#include "jy901s_app.h"

/*组件库*/
#include "jy901s_driver.h"

extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart5;
extern DMA_HandleTypeDef hdma_usart5_rx;

/*uart5*/
uint8_t uart5_rx_buffer[32];
//测试使用
uint8_t uart5_flag = 0;

// JY901S陀螺仪实体
JY901S_t jy901s;  

void jy901s_task(void)
{
//	if(uart5_flag)
//	{
//		uart5_flag = 0;
//		
//		my_printf(&huart1, "uart5:%s\r\n", uart5_rx_buffer);
//		my_printf(&huart5, "uart5:%s\r\n", uart5_rx_buffer);

//		HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
//	}
	
#if JY901S_WATCHDOG_ENABLE
    // 看门狗检查（集成到主任务中）
    static uint32_t last_watchdog_check = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_watchdog_check >= JY901S_WATCHDOG_CHECK_INTERVAL) {
        JY901S_WatchdogCheck(&jy901s);
        last_watchdog_check = current_time;
    }
#endif

    // 获取完整数据
	JY901S_Data_t* data = JY901S_GetData(&jy901s);
	if (data != NULL)
	{
//			my_printf(&huart1, "Roll: %.2f,Pitch: %.2f,Yaw: %.2f\r\n", data->roll, data->pitch, data->yaw);
		  my_printf(&huart1, "%.2f,%.2f,%.2f\n", data->roll, data->pitch, data->yaw);
//			my_printf(&huart1, "%.2f,%.2f,%.2f\n", data->acc_x, data->acc_y, data->acc_z);
//			my_printf(&huart1, "%.2f,%.2f,%.2f\n", data->gyro_x, data->gyro_y, data->gyro_z);
//			my_printf(&huart1, "%.2f,%.2f,%.2f\n", data->mag_x, data->mag_y, data->mag_z);
	}
}

// 在DMA接收完成回调中处理数据
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART5)
    {
        // 处理接收到的数据
        JY901S_ProcessBuffer(&jy901s, uart5_rx_buffer, sizeof(uart5_rx_buffer));
				//测试使用
//        uart5_flag = 1;
        // 重新启动DMA接收 收满回调 定长数据
        HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
    }
}

void jy901s_init()
{
	// 启用串口2 imu专用串口
	HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
	
	JY901S_Create(&jy901s, &huart5, 1000); 
	
//	jy901s_set();
	
//	jy901s_calibration();
}
